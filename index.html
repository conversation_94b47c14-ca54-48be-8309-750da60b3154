<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>二维码生成器 - QR Code Generator</title>
    
    <!-- Tailwind CSS -->
    <script src="https://unpkg.com/@tailwindcss/browser@4"></script>
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.2/css/all.min.css" rel="stylesheet">
    
    <!-- QRCode.js -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/qrcode/1.5.3/qrcode.min.js"></script>
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Inter', sans-serif;
        }
        
        /* 自定义动画 */
        .animate-fade-in {
            animation: fadeIn 0.5s ease-in-out;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .hover-lift {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }
        
        .hover-lift:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            transition: all 0.3s ease;
        }
        
        .btn-primary:hover {
            background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
        }
        
        /* 平滑滚动 */
        html {
            scroll-behavior: smooth;
        }
        
        /* 自定义滚动条 */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: #f1f5f9;
        }

        ::-webkit-scrollbar-thumb {
            background: #cbd5e1;
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: #94a3b8;
        }

        /* 滑块样式 */
        .slider {
            background: linear-gradient(to right, #e2e8f0 0%, #e2e8f0 100%);
        }

        .slider::-webkit-slider-thumb {
            appearance: none;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: #3b82f6;
            cursor: pointer;
            border: 2px solid #ffffff;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .slider::-moz-range-thumb {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: #3b82f6;
            cursor: pointer;
            border: 2px solid #ffffff;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        /* 加载动画 */
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #3b82f6;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* 成功提示动画 */
        .success-pulse {
            animation: successPulse 0.6s ease-in-out;
        }

        @keyframes successPulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        /* 错误提示样式 */
        .error-shake {
            animation: errorShake 0.5s ease-in-out;
        }

        @keyframes errorShake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }

        /* 响应式字体大小 */
        @media (max-width: 640px) {
            .text-responsive {
                font-size: 0.875rem;
            }
        }

        /* 高对比度模式支持 */
        @media (prefers-contrast: high) {
            .bg-gradient-to-br {
                background: #ffffff;
            }

            .border-slate-200 {
                border-color: #000000;
            }
        }

        /* 减少动画模式支持 */
        @media (prefers-reduced-motion: reduce) {
            * {
                animation-duration: 0.01ms !important;
                animation-iteration-count: 1 !important;
                transition-duration: 0.01ms !important;
            }
        }
    </style>
</head>
<body class="bg-gradient-to-br from-slate-50 to-blue-50 min-h-screen">
    <!-- 导航栏 -->
    <nav class="bg-white/80 backdrop-blur-md border-b border-slate-200 sticky top-0 z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                        <i class="fas fa-qrcode text-white text-sm"></i>
                    </div>
                    <h1 class="text-xl font-semibold text-slate-800">二维码生成器</h1>
                </div>
                <div class="hidden md:flex items-center space-x-6">
                    <a href="#generator" class="text-slate-600 hover:text-slate-900 transition-colors">生成器</a>
                    <a href="#customization" class="text-slate-600 hover:text-slate-900 transition-colors">自定义</a>
                    <a href="#about" class="text-slate-600 hover:text-slate-900 transition-colors">关于</a>
                </div>
                <button class="md:hidden p-2 rounded-lg hover:bg-slate-100 transition-colors" id="mobile-menu-btn">
                    <i class="fas fa-bars text-slate-600"></i>
                </button>
            </div>
        </div>
        
        <!-- 移动端菜单 -->
        <div class="md:hidden bg-white border-t border-slate-200 hidden" id="mobile-menu">
            <div class="px-4 py-3 space-y-2">
                <a href="#generator" class="block py-2 text-slate-600 hover:text-slate-900 transition-colors">生成器</a>
                <a href="#customization" class="block py-2 text-slate-600 hover:text-slate-900 transition-colors">自定义</a>
                <a href="#about" class="block py-2 text-slate-600 hover:text-slate-900 transition-colors">关于</a>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div class="grid lg:grid-cols-3 gap-8">
            <!-- 左侧：输入和生成区域 -->
            <div class="lg:col-span-2 space-y-6">
                <!-- 输入区域 -->
                <section id="generator" class="bg-white rounded-2xl shadow-sm border border-slate-200 p-6 hover-lift animate-fade-in">
                    <div class="flex items-center space-x-3 mb-6">
                        <div class="w-10 h-10 bg-blue-100 rounded-xl flex items-center justify-center">
                            <i class="fas fa-edit text-blue-600"></i>
                        </div>
                        <div>
                            <h2 class="text-xl font-semibold text-slate-800">输入内容</h2>
                            <p class="text-sm text-slate-500">输入链接或文本生成二维码</p>
                        </div>
                    </div>
                    
                    <div class="space-y-4">
                        <div>
                            <label for="qr-input" class="block text-sm font-medium text-slate-700 mb-2">
                                内容 <span class="text-red-500">*</span>
                            </label>
                            <textarea 
                                id="qr-input" 
                                rows="4" 
                                class="w-full px-4 py-3 border border-slate-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all resize-none"
                                placeholder="请输入要生成二维码的文本或链接..."
                                aria-describedby="input-help"
                            ></textarea>
                            <p id="input-help" class="mt-2 text-xs text-slate-500">
                                支持网址、文本、电话号码等各种内容
                            </p>
                        </div>
                        
                        <button 
                            id="generate-btn" 
                            class="w-full btn-primary text-white font-medium py-3 px-6 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                            aria-describedby="generate-help"
                        >
                            <i class="fas fa-magic mr-2"></i>
                            生成二维码
                        </button>
                        <p id="generate-help" class="text-xs text-slate-500">
                            点击按钮生成可在微信中扫描的二维码
                        </p>
                    </div>
                </section>

                <!-- 二维码显示区域 -->
                <section class="bg-white rounded-2xl shadow-sm border border-slate-200 p-6 hover-lift">
                    <div class="flex items-center justify-between mb-6">
                        <div class="flex items-center space-x-3">
                            <div class="w-10 h-10 bg-green-100 rounded-xl flex items-center justify-center">
                                <i class="fas fa-qrcode text-green-600"></i>
                            </div>
                            <div>
                                <h2 class="text-xl font-semibold text-slate-800">生成结果</h2>
                                <p class="text-sm text-slate-500">您的二维码将显示在这里</p>
                            </div>
                        </div>
                        <div class="flex space-x-2">
                            <button
                                id="copy-btn"
                                class="hidden bg-blue-100 hover:bg-blue-200 text-blue-700 px-4 py-2 rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500"
                                aria-label="复制二维码"
                                title="复制二维码到剪贴板"
                            >
                                <i class="fas fa-copy mr-2"></i>
                                复制
                            </button>
                            <button
                                id="download-btn"
                                class="hidden bg-slate-100 hover:bg-slate-200 text-slate-700 px-4 py-2 rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-slate-500"
                                aria-label="下载二维码"
                                title="下载二维码为PNG文件"
                            >
                                <i class="fas fa-download mr-2"></i>
                                下载
                            </button>
                        </div>
                    </div>
                    
                    <div id="qr-container" class="flex items-center justify-center min-h-64 bg-slate-50 rounded-xl border-2 border-dashed border-slate-300">
                        <div class="text-center text-slate-400">
                            <i class="fas fa-qrcode text-4xl mb-4"></i>
                            <p class="text-lg font-medium">等待生成二维码</p>
                            <p class="text-sm">请在上方输入内容并点击生成</p>
                        </div>
                    </div>
                </section>
            </div>

            <!-- 右侧：自定义选项 -->
            <div class="space-y-6">
                <section id="customization" class="bg-white rounded-2xl shadow-sm border border-slate-200 p-6 hover-lift animate-fade-in">
                    <div class="flex items-center space-x-3 mb-6">
                        <div class="w-10 h-10 bg-purple-100 rounded-xl flex items-center justify-center">
                            <i class="fas fa-palette text-purple-600"></i>
                        </div>
                        <div>
                            <h2 class="text-xl font-semibold text-slate-800">自定义样式</h2>
                            <p class="text-sm text-slate-500">个性化您的二维码</p>
                        </div>
                    </div>
                    
                    <div class="space-y-6">
                        <!-- 颜色设置 -->
                        <div>
                            <h3 class="text-sm font-medium text-slate-700 mb-3">颜色设置</h3>
                            <div class="grid grid-cols-2 gap-4">
                                <div>
                                    <label for="foreground-color" class="block text-xs text-slate-600 mb-2">前景色</label>
                                    <div class="flex items-center space-x-2">
                                        <input 
                                            type="color" 
                                            id="foreground-color" 
                                            value="#000000" 
                                            class="w-8 h-8 rounded border border-slate-300 cursor-pointer"
                                            aria-label="选择前景色"
                                        >
                                        <span id="fg-color-text" class="text-xs text-slate-500">#000000</span>
                                    </div>
                                </div>
                                <div>
                                    <label for="background-color" class="block text-xs text-slate-600 mb-2">背景色</label>
                                    <div class="flex items-center space-x-2">
                                        <input 
                                            type="color" 
                                            id="background-color" 
                                            value="#ffffff" 
                                            class="w-8 h-8 rounded border border-slate-300 cursor-pointer"
                                            aria-label="选择背景色"
                                        >
                                        <span id="bg-color-text" class="text-xs text-slate-500">#ffffff</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 尺寸设置 -->
                        <div>
                            <label for="qr-size" class="block text-sm font-medium text-slate-700 mb-3">
                                尺寸大小: <span id="size-value" class="text-blue-600">256px</span>
                            </label>
                            <input 
                                type="range" 
                                id="qr-size" 
                                min="128" 
                                max="512" 
                                value="256" 
                                class="w-full h-2 bg-slate-200 rounded-lg appearance-none cursor-pointer slider"
                                aria-label="调整二维码尺寸"
                            >
                            <div class="flex justify-between text-xs text-slate-500 mt-1">
                                <span>128px</span>
                                <span>512px</span>
                            </div>
                        </div>

                        <!-- 容错级别 -->
                        <div>
                            <label for="error-level" class="block text-sm font-medium text-slate-700 mb-3">容错级别</label>
                            <select 
                                id="error-level" 
                                class="w-full px-3 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                aria-describedby="error-level-help"
                            >
                                <option value="L">低 (L) - 约7%</option>
                                <option value="M" selected>中 (M) - 约15%</option>
                                <option value="Q">较高 (Q) - 约25%</option>
                                <option value="H">高 (H) - 约30%</option>
                            </select>
                            <p id="error-level-help" class="mt-1 text-xs text-slate-500">
                                容错级别越高，二维码越能抵抗损坏
                            </p>
                        </div>

                        <!-- 预设样式 -->
                        <div>
                            <h3 class="text-sm font-medium text-slate-700 mb-3">预设样式</h3>
                            <div class="grid grid-cols-2 gap-2">
                                <button class="preset-style p-3 border-2 border-slate-200 rounded-lg hover:border-blue-500 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500" data-fg="#000000" data-bg="#ffffff">
                                    <div class="w-6 h-6 bg-black mx-auto mb-1"></div>
                                    <span class="text-xs">经典</span>
                                </button>
                                <button class="preset-style p-3 border-2 border-slate-200 rounded-lg hover:border-blue-500 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500" data-fg="#1e40af" data-bg="#dbeafe">
                                    <div class="w-6 h-6 bg-blue-700 mx-auto mb-1"></div>
                                    <span class="text-xs">蓝色</span>
                                </button>
                                <button class="preset-style p-3 border-2 border-slate-200 rounded-lg hover:border-blue-500 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500" data-fg="#059669" data-bg="#d1fae5">
                                    <div class="w-6 h-6 bg-green-600 mx-auto mb-1"></div>
                                    <span class="text-xs">绿色</span>
                                </button>
                                <button class="preset-style p-3 border-2 border-slate-200 rounded-lg hover:border-blue-500 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500" data-fg="#7c3aed" data-bg="#ede9fe">
                                    <div class="w-6 h-6 bg-purple-600 mx-auto mb-1"></div>
                                    <span class="text-xs">紫色</span>
                                </button>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- 关于信息 -->
                <section id="about" class="bg-white rounded-2xl shadow-sm border border-slate-200 p-6 hover-lift">
                    <div class="flex items-center space-x-3 mb-4">
                        <div class="w-10 h-10 bg-amber-100 rounded-xl flex items-center justify-center">
                            <i class="fas fa-info-circle text-amber-600"></i>
                        </div>
                        <div>
                            <h2 class="text-xl font-semibold text-slate-800">使用说明</h2>
                        </div>
                    </div>

                    <div class="space-y-3 text-sm text-slate-600">
                        <div class="flex items-start space-x-2">
                            <i class="fas fa-check-circle text-green-500 mt-0.5"></i>
                            <span>支持文本、链接、电话等多种内容</span>
                        </div>
                        <div class="flex items-start space-x-2">
                            <i class="fas fa-check-circle text-green-500 mt-0.5"></i>
                            <span>生成的二维码兼容微信扫描</span>
                        </div>
                        <div class="flex items-start space-x-2">
                            <i class="fas fa-check-circle text-green-500 mt-0.5"></i>
                            <span>可自定义颜色和尺寸</span>
                        </div>
                        <div class="flex items-start space-x-2">
                            <i class="fas fa-check-circle text-green-500 mt-0.5"></i>
                            <span>支持下载为PNG格式</span>
                        </div>
                    </div>
                </section>

                <!-- 快捷键提示 -->
                <section class="bg-white rounded-2xl shadow-sm border border-slate-200 p-6 hover-lift">
                    <div class="flex items-center space-x-3 mb-4">
                        <div class="w-10 h-10 bg-indigo-100 rounded-xl flex items-center justify-center">
                            <i class="fas fa-keyboard text-indigo-600"></i>
                        </div>
                        <div>
                            <h2 class="text-xl font-semibold text-slate-800">快捷键</h2>
                        </div>
                    </div>

                    <div class="space-y-2 text-sm text-slate-600">
                        <div class="flex justify-between items-center">
                            <span>生成二维码</span>
                            <kbd class="px-2 py-1 bg-slate-100 rounded text-xs font-mono">Ctrl+Enter</kbd>
                        </div>
                        <div class="flex justify-between items-center">
                            <span>下载二维码</span>
                            <kbd class="px-2 py-1 bg-slate-100 rounded text-xs font-mono">Ctrl+S</kbd>
                        </div>
                        <div class="flex justify-between items-center">
                            <span>复制二维码</span>
                            <kbd class="px-2 py-1 bg-slate-100 rounded text-xs font-mono">Ctrl+C</kbd>
                        </div>
                        <div class="flex justify-between items-center">
                            <span>清空输入</span>
                            <kbd class="px-2 py-1 bg-slate-100 rounded text-xs font-mono">Esc</kbd>
                        </div>
                    </div>
                </section>
            </div>
        </div>
    </main>

    <!-- 页脚 -->
    <footer class="bg-white border-t border-slate-200 mt-16">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <div class="text-center text-slate-500">
                <p class="text-sm">© 2024 二维码生成器. 简单、快速、安全的二维码生成工具.</p>
            </div>
        </div>
    </footer>

    <script>
        // 全局变量
        let currentQRCode = null;
        
        // DOM 元素
        const qrInput = document.getElementById('qr-input');
        const generateBtn = document.getElementById('generate-btn');
        const downloadBtn = document.getElementById('download-btn');
        const copyBtn = document.getElementById('copy-btn');
        const qrContainer = document.getElementById('qr-container');
        const foregroundColor = document.getElementById('foreground-color');
        const backgroundColor = document.getElementById('background-color');
        const qrSize = document.getElementById('qr-size');
        const errorLevel = document.getElementById('error-level');
        const sizeValue = document.getElementById('size-value');
        const fgColorText = document.getElementById('fg-color-text');
        const bgColorText = document.getElementById('bg-color-text');
        const presetStyles = document.querySelectorAll('.preset-style');
        const mobileMenuBtn = document.getElementById('mobile-menu-btn');
        const mobileMenu = document.getElementById('mobile-menu');

        // 移动端菜单切换
        mobileMenuBtn.addEventListener('click', () => {
            mobileMenu.classList.toggle('hidden');
        });

        // 显示通知函数
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg max-w-sm transform transition-all duration-300 translate-x-full`;

            const bgColor = type === 'success' ? 'bg-green-500' : type === 'error' ? 'bg-red-500' : 'bg-blue-500';
            notification.className += ` ${bgColor} text-white`;

            const icon = type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-triangle' : 'info-circle';

            notification.innerHTML = `
                <div class="flex items-center space-x-3">
                    <i class="fas fa-${icon}"></i>
                    <span class="text-sm font-medium">${message}</span>
                    <button class="ml-auto text-white hover:text-gray-200" onclick="this.parentElement.parentElement.remove()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            `;

            document.body.appendChild(notification);

            // 显示动画
            setTimeout(() => {
                notification.classList.remove('translate-x-full');
            }, 100);

            // 自动消失
            setTimeout(() => {
                notification.classList.add('translate-x-full');
                setTimeout(() => {
                    if (notification.parentElement) {
                        notification.remove();
                    }
                }, 300);
            }, 3000);
        }

        // 验证输入内容
        function validateInput(text) {
            if (!text || text.length === 0) {
                return { valid: false, message: '请输入要生成二维码的内容' };
            }

            if (text.length > 2000) {
                return { valid: false, message: '输入内容过长，请控制在2000字符以内' };
            }

            return { valid: true };
        }

        // 检查颜色对比度
        function checkColorContrast(fg, bg) {
            // 简单的对比度检查
            const fgRgb = hexToRgb(fg);
            const bgRgb = hexToRgb(bg);

            if (!fgRgb || !bgRgb) return true;

            const fgLuminance = getLuminance(fgRgb);
            const bgLuminance = getLuminance(bgRgb);

            const contrast = (Math.max(fgLuminance, bgLuminance) + 0.05) / (Math.min(fgLuminance, bgLuminance) + 0.05);

            return contrast >= 3; // 最低对比度要求
        }

        function hexToRgb(hex) {
            const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
            return result ? {
                r: parseInt(result[1], 16),
                g: parseInt(result[2], 16),
                b: parseInt(result[3], 16)
            } : null;
        }

        function getLuminance(rgb) {
            const { r, g, b } = rgb;
            const [rs, gs, bs] = [r, g, b].map(c => {
                c = c / 255;
                return c <= 0.03928 ? c / 12.92 : Math.pow((c + 0.055) / 1.055, 2.4);
            });
            return 0.2126 * rs + 0.7152 * gs + 0.0722 * bs;
        }

        // 生成二维码函数
        function generateQRCode() {
            const text = qrInput.value.trim();

            // 验证输入
            const validation = validateInput(text);
            if (!validation.valid) {
                showNotification(validation.message, 'error');
                qrInput.classList.add('error-shake');
                setTimeout(() => qrInput.classList.remove('error-shake'), 500);
                qrInput.focus();
                return;
            }

            // 检查颜色对比度
            if (!checkColorContrast(foregroundColor.value, backgroundColor.value)) {
                showNotification('前景色和背景色对比度过低，可能影响扫描效果', 'error');
                return;
            }

            // 显示加载状态
            generateBtn.disabled = true;
            generateBtn.innerHTML = '<div class="loading mr-2"></div>生成中...';

            // 清空容器
            qrContainer.innerHTML = '';

            // 创建canvas元素
            const canvas = document.createElement('canvas');
            qrContainer.appendChild(canvas);

            // 生成二维码
            QRCode.toCanvas(canvas, text, {
                width: parseInt(qrSize.value),
                height: parseInt(qrSize.value),
                color: {
                    dark: foregroundColor.value,
                    light: backgroundColor.value
                },
                errorCorrectionLevel: errorLevel.value,
                margin: 2
            }, function (error) {
                // 恢复按钮状态
                generateBtn.disabled = false;
                generateBtn.innerHTML = '<i class="fas fa-magic mr-2"></i>生成二维码';

                if (error) {
                    console.error(error);
                    qrContainer.innerHTML = `
                        <div class="text-center text-red-500">
                            <i class="fas fa-exclamation-triangle text-4xl mb-4"></i>
                            <p class="text-lg font-medium">生成失败</p>
                            <p class="text-sm">请检查输入内容或稍后重试</p>
                        </div>
                    `;
                    showNotification('二维码生成失败，请检查输入内容', 'error');
                } else {
                    currentQRCode = canvas;
                    downloadBtn.classList.remove('hidden');
                    copyBtn.classList.remove('hidden');

                    // 添加样式和成功动画
                    canvas.className = 'mx-auto rounded-lg shadow-sm success-pulse';

                    // 添加点击事件到canvas（用于键盘快捷键）
                    canvas.tabIndex = 0;
                    canvas.setAttribute('aria-label', '生成的二维码，可使用Ctrl+C复制');

                    showNotification('二维码生成成功！', 'success');

                    // 移除动画类
                    setTimeout(() => {
                        canvas.classList.remove('success-pulse');
                    }, 600);
                }
            });
        }

        // 下载二维码函数
        function downloadQRCode() {
            if (!currentQRCode) return;

            const link = document.createElement('a');
            const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
            link.download = `qrcode-${timestamp}.png`;
            link.href = currentQRCode.toDataURL();
            link.click();

            showNotification('二维码已下载', 'success');
        }

        // 复制二维码函数
        async function copyQRCode() {
            if (!currentQRCode) return;

            try {
                // 将canvas转换为blob
                currentQRCode.toBlob(async (blob) => {
                    try {
                        await navigator.clipboard.write([
                            new ClipboardItem({
                                'image/png': blob
                            })
                        ]);
                        showNotification('二维码已复制到剪贴板', 'success');
                    } catch (err) {
                        console.error('复制失败:', err);
                        // 降级方案：复制数据URL
                        try {
                            await navigator.clipboard.writeText(currentQRCode.toDataURL());
                            showNotification('二维码数据已复制到剪贴板', 'success');
                        } catch (err2) {
                            showNotification('复制失败，请手动保存', 'error');
                        }
                    }
                });
            } catch (err) {
                console.error('复制失败:', err);
                showNotification('复制失败，请手动保存', 'error');
            }
        }

        // 更新尺寸显示
        function updateSizeDisplay() {
            sizeValue.textContent = qrSize.value + 'px';
        }

        // 更新颜色显示
        function updateColorDisplay() {
            fgColorText.textContent = foregroundColor.value;
            bgColorText.textContent = backgroundColor.value;
        }

        // 应用预设样式
        function applyPresetStyle(fg, bg) {
            foregroundColor.value = fg;
            backgroundColor.value = bg;
            updateColorDisplay();
            
            // 如果已有二维码，重新生成
            if (currentQRCode && qrInput.value.trim()) {
                generateQRCode();
            }
        }

        // 键盘快捷键处理
        function handleKeyboardShortcuts(e) {
            // Ctrl/Cmd + Enter: 生成二维码
            if ((e.ctrlKey || e.metaKey) && e.key === 'Enter') {
                e.preventDefault();
                generateQRCode();
                return;
            }

            // Ctrl/Cmd + S: 下载二维码
            if ((e.ctrlKey || e.metaKey) && e.key === 's' && currentQRCode) {
                e.preventDefault();
                downloadQRCode();
                return;
            }

            // Ctrl/Cmd + C: 复制二维码（当焦点在二维码区域时）
            if ((e.ctrlKey || e.metaKey) && e.key === 'c' && currentQRCode &&
                (e.target === qrContainer || qrContainer.contains(e.target))) {
                e.preventDefault();
                copyQRCode();
                return;
            }

            // Escape: 清空输入
            if (e.key === 'Escape' && document.activeElement === qrInput) {
                qrInput.value = '';
                qrInput.focus();
                return;
            }
        }

        // 事件监听器
        generateBtn.addEventListener('click', generateQRCode);
        downloadBtn.addEventListener('click', downloadQRCode);
        copyBtn.addEventListener('click', copyQRCode);
        qrSize.addEventListener('input', updateSizeDisplay);
        foregroundColor.addEventListener('change', updateColorDisplay);
        backgroundColor.addEventListener('change', updateColorDisplay);

        // 键盘事件
        document.addEventListener('keydown', handleKeyboardShortcuts);

        // 输入框回车生成
        qrInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                generateQRCode();
            }
        });

        // 预设样式点击事件
        presetStyles.forEach(btn => {
            btn.addEventListener('click', () => {
                const fg = btn.dataset.fg;
                const bg = btn.dataset.bg;
                applyPresetStyle(fg, bg);
                
                // 更新选中状态
                presetStyles.forEach(b => b.classList.remove('border-blue-500', 'bg-blue-50'));
                btn.classList.add('border-blue-500', 'bg-blue-50');
            });
        });

        // 颜色变化时重新生成二维码
        [foregroundColor, backgroundColor, qrSize, errorLevel].forEach(element => {
            element.addEventListener('change', () => {
                if (currentQRCode && qrInput.value.trim()) {
                    generateQRCode();
                }
            });
        });

        // 平滑滚动到锚点
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
                
                // 关闭移动端菜单
                mobileMenu.classList.add('hidden');
            });
        });

        // 初始化
        updateSizeDisplay();
        updateColorDisplay();
        
        // 设置焦点到输入框
        qrInput.focus();
    </script>
</body>
</html>
