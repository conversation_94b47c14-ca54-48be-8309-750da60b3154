# 二维码生成器 (QR Code Generator)

一个功能完整、界面美观的二维码生成网页工具，支持自定义样式和移动端响应式设计。

## 功能特性

### 🎯 核心功能
- **文本/链接输入** - 支持各种类型的文本、网址、电话号码等
- **二维码生成** - 生成可在微信中扫描的高质量二维码
- **实时预览** - 输入内容后即时生成二维码预览
- **下载功能** - 支持将二维码保存为PNG格式文件
- **复制功能** - 一键复制二维码到剪贴板

### 🎨 自定义选项
- **颜色设置** - 自定义前景色和背景色
- **尺寸调节** - 128px到512px可调节尺寸
- **容错级别** - 四种容错级别可选（L/M/Q/H）
- **预设样式** - 经典、蓝色、绿色、紫色四种预设配色

### 📱 用户体验
- **响应式设计** - 完美适配桌面端和移动端
- **现代化UI** - 使用Tailwind CSS构建的现代界面
- **微动画效果** - 悬停和交互时的平滑动画
- **键盘快捷键** - 支持快捷键操作提高效率
- **无障碍支持** - 符合WCAG可访问性标准

## 技术栈

- **HTML5** - 语义化标记结构
- **CSS3** - 现代样式和动画
- **JavaScript** - 原生JS实现交互功能
- **Tailwind CSS** - 实用优先的CSS框架
- **Font Awesome** - 图标库
- **QRCode.js** - 二维码生成库
- **Inter字体** - 现代无衬线字体

## 快捷键

| 功能 | 快捷键 |
|------|--------|
| 生成二维码 | `Ctrl + Enter` |
| 下载二维码 | `Ctrl + S` |
| 复制二维码 | `Ctrl + C` |
| 清空输入 | `Esc` |

## 浏览器支持

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## 使用方法

1. 在输入框中输入要生成二维码的内容
2. 点击"生成二维码"按钮或使用快捷键 `Ctrl + Enter`
3. 在右侧自定义面板中调整颜色、尺寸等设置
4. 使用下载或复制功能保存二维码

## 项目结构

```
CreateEWM/
├── index.html          # 主页面文件
└── README.md           # 项目说明文档
```

## 特色亮点

### 🎯 用户友好
- 直观的操作界面，无需学习成本
- 实时反馈和错误提示
- 支持多种输入格式验证

### 🎨 视觉设计
- 遵循现代Web设计原则
- 自然柔和的配色方案
- 清晰的视觉层级和信息架构

### 📱 响应式体验
- 移动端优先的设计理念
- 汉堡菜单和触摸友好的交互
- 不同屏幕尺寸的完美适配

### ♿ 可访问性
- 颜色对比度符合AA级标准
- 完整的键盘导航支持
- ARIA标签和语义化HTML

### ⚡ 性能优化
- 轻量级依赖和快速加载
- 优化的图片和资源
- 平滑的动画和交互

## 开发说明

本项目采用单页面应用架构，所有功能都集成在一个HTML文件中，便于部署和维护。代码结构清晰，注释完整，易于理解和扩展。

## 许可证

MIT License - 可自由使用、修改和分发。

## 作者

Created with ❤️ using modern web technologies.
